import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })

  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Check for guest session in cookies
  const guestSessionCookie = req.cookies.get('guestSession')
  const hasGuestSession = guestSessionCookie && guestSessionCookie.value

  console.log('Middleware - Current path:', req.nextUrl.pathname)
  console.log('Middleware - Session:', session ? 'exists' : 'none')

  // If there's no session and no guest session, and the user is trying to access a protected route
  if (!session && !hasGuestSession && !req.nextUrl.pathname.startsWith('/login') && !req.nextUrl.pathname.startsWith('/register')) {
    console.log('Middleware - Redirecting to login')
    return NextResponse.redirect(new URL('/login', req.url))
  }

  // If there's a session and the user is trying to access auth routes
  if (session && (req.nextUrl.pathname.startsWith('/login') || req.nextUrl.pathname.startsWith('/register'))) {
    console.log('Middleware - Redirecting to home')
    return NextResponse.redirect(new URL('/', req.url))
  }

  return res
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
} 