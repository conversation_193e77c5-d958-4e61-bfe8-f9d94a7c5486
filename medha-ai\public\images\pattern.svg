<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" stroke-width="0.5" opacity="0.2"/>
    </pattern>
    <pattern id="circles" width="50" height="50" patternUnits="userSpaceOnUse">
      <circle cx="25" cy="25" r="15" fill="none" stroke="white" stroke-width="0.5" opacity="0.1"/>
      <circle cx="25" cy="25" r="8" fill="none" stroke="white" stroke-width="0.5" opacity="0.15"/>
    </pattern>
  </defs>
  <rect width="100" height="100" fill="url(#grid)"/>
  <rect width="100" height="100" fill="url(#circles)"/>
</svg> 