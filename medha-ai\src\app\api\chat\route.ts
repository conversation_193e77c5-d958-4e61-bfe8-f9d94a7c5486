import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { message } = await req.json()

    // TODO: Integrate with DeepSeek LLM
    // For now, return a mock response
    const response = {
      message: "I'm your AI study assistant. I can help you with your IGCSE subjects. What would you like to learn about?",
      subject: "general",
      confidence: 0.95
    }

    // Store the chat message in the database
    const { error: dbError } = await supabase
      .from('chat_messages')
      .insert([
        {
          user_id: session.user.id,
          message,
          response: response.message,
          subject: response.subject,
          confidence: response.confidence
        }
      ])

    if (dbError) {
      console.error('Error storing chat message:', dbError)
      return NextResponse.json({ error: 'Failed to store message' }, { status: 500 })
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Chat API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 