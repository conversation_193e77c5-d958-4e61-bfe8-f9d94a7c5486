import { notFound } from 'next/navigation'

const SUBJECTS = {
  chemistry: {
    name: 'Chemistry',
    icon: '🧪',
    description: 'Study of matter, its properties, and transformations',
    chapters: [
      { id: 'atomic-structure', name: 'Atomic Structure' },
      { id: 'bonding', name: 'Bonding' },
      { id: 'reactions', name: 'Chemical Reactions' }
    ]
  },
  physics: {
    name: 'Physics',
    icon: '⚡',
    description: 'Understanding the laws of nature and energy',
    chapters: [
      { id: 'forces', name: 'Forces and Motion' },
      { id: 'energy', name: 'Energy' },
      { id: 'waves', name: 'Waves' }
    ]
  },
  // Add other subjects as needed
}

export default function SubjectPage({ params }: { params: { id: string } }) {
  const subject = SUBJECTS[params.id as keyof typeof SUBJECTS]

  if (!subject) {
    notFound()
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-8">
        <span className="text-4xl">{subject.icon}</span>
        <div>
          <h1 className="text-3xl font-bold">{subject.name}</h1>
          <p className="text-gray-600 dark:text-gray-400">{subject.description}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {subject.chapters.map((chapter) => (
          <div
            key={chapter.id}
            className="p-6 border rounded-lg hover:shadow-lg transition-shadow cursor-pointer"
          >
            <h2 className="text-xl font-semibold">{chapter.name}</h2>
          </div>
        ))}
      </div>
    </div>
  )
} 