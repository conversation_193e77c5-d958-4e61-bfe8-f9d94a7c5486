'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"

interface SubjectTileProps {
  id: string
  name: string
  icon: string
  description: string
}

export default function SubjectTile({ id, name, icon, description }: SubjectTileProps) {
  const router = useRouter()

  return (
    <Card 
      className="cursor-pointer hover:shadow-lg transition-shadow"
      onClick={() => router.push(`/subjects/${id}`)}
    >
      <CardHeader>
        <div className="flex items-center gap-2">
          <span className="text-2xl">{icon}</span>
          <CardTitle>{name}</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-600 dark:text-gray-400">{description}</p>
      </CardContent>
    </Card>
  )
} 