# Medha-AI: IGCSE Study Assistant App - Developer Guide

## Overview
Medha-AI is a web application designed to assist Cambridge IGCSE Grade 9 and 10 students in studying effectively. It integrates a chatbot interface powered by the DeepSeek LLM hosted on HuggingFace. The chatbot provides subject-specific help, flashcards, quizzes, study tips, and more.

## Core Objectives
- Deliver clear, concise, and accurate answers for IGCSE subject-related queries.
- Enable interactive and adaptive learning.
- Provide revision and testing tools to enhance student engagement.
- Strictly educational responses only—no LLM/system explanations.

## Target Subjects
- Chemistry
- Physics
- Biology
- Mathematics
- Economics
- English (Language and Literature)

---

## App Flow

### 1. **Onboarding / Welcome Screen**
- Brief welcome message.
- Ask for student’s name and class (9 or 10).
- Short explanation of how Medha-AI helps with IGCSE study.

### 2. **Home Screen**
- Display subject tiles (e.g., Chemistry, Physics, etc.).
- Option to "Chat with Medha-AI" at the top.
- Option to view Flashcards, Quizzes, Study Tips.

### 3. **Chatbot Interface**
- Natural language input box.
- Displays helpful, educational responses only.
- Chatbot answers only subject-related academic questions.
- Politely refuses to answer non-educational or app/system-related queries.
- Implements follow-up questions and adaptive learning logic.

#### Chatbot Features:
- Detects question subject and topic.
- Provides step-by-step problem-solving guidance.
- Uses spaced repetition and active recall techniques.

### 4. **Flashcards Section**
- Subject → Chapter → Topic hierarchy.
- Flashcards contain:
  - Key concepts
  - Definitions
  - Examples (where applicable)
- Option to mark as "Known" or "Review Again".

### 5. **Quiz Section**
- Subject → Chapter → Topic selection.
- Difficulty levels:
  - Easy
  - Medium
  - Hard
- Multiple choice and fill-in-the-blank type questions.
- Instant feedback with explanation.
- Scores and progress tracker.

### 6. **Study Tips & Exam Techniques**
- Time management strategies.
- How to tackle each type of question (MCQs, long-form, etc.).
- Memory techniques (mnemonics, active recall).
- Encouragement messages to promote self-study.

### 7. **Progress & Analytics (Optional)**
- Daily/weekly progress report.
- Time spent per subject.
- Flashcard and quiz performance.

---

## Technical Integration Notes
- **LLM Integration**: Uses DeepSeek LLM hosted on HuggingFace.
- **API Calls**: Chat screen connects to backend via REST API or WebSocket.
- **Input Filtering**: Ensure non-educational prompts are filtered and answered with redirection to study content.
- **Data Storage**: Use local storage or cloud DB (e.g., Firebase) to track user progress.

---

## Rules & Restrictions
- No responses about how Medha-AI, LLMs, or prompt engineering works.
- No developer or app usage instructions shared by chatbot.
- Strictly academic assistance only.

---

## UX/UI Considerations
- Clean, minimal interface.
- Use icons for subjects.
- Highlight active chat sessions.
- Easy access to flashcards and quizzes from the home screen.
- Friendly and motivational chatbot tone.

---

## Future Enhancements (Optional)
- Add voice input for questions.
- Save and bookmark important questions.
- Leaderboard or gamification for quiz section.
- Offline mode for flashcards and quizzes.

---

## Conclusion
Medha-AI is focused on helping IGCSE students succeed by providing structured, intelligent, and engaging educational assistance. Developers should adhere to the app's goal of maintaining a purely academic and helpful environment for learners.

---

*End of Document*

