'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { useRouter } from "next/navigation"
import { useState, FormEvent } from "react"

export default function HomePage() {
  const router = useRouter()
  const [message, setMessage] = useState("")

  const handleSendMessage = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (message.trim()) {
      router.push(`/chat?message=${encodeURIComponent(message)}`)
      setMessage("")
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <a className="mr-6 flex items-center space-x-2" href="/">
              <span className="font-bold">Medha-AI</span>
            </a>
          </div>
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <Button variant="outline" onClick={() => router.push('/chat')}>
                Start Chat
              </Button>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-1">
        <section className="container grid items-center gap-6 pb-8 pt-6 md:py-10">
          <div className="flex max-w-[980px] flex-col items-start gap-2">
            <h1 className="text-3xl font-extrabold leading-tight tracking-tighter md:text-4xl">
              Your Personal IGCSE Study Assistant
            </h1>
            <p className="max-w-[750px] text-lg text-muted-foreground sm:text-xl">
              Get instant help with your IGCSE subjects. Ask questions, get explanations, and improve your understanding.
            </p>
          </div>
          <div className="flex gap-4">
            <Button onClick={() => router.push('/chat')}>Start Learning</Button>
            <Button variant="outline">Learn More</Button>
          </div>
        </section>
        <section className="container grid gap-6 py-8 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Chemistry</CardTitle>
              <CardDescription>Master chemical concepts and reactions</CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full" onClick={() => router.push('/subjects/chemistry')}>
                Explore Chemistry
              </Button>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Physics</CardTitle>
              <CardDescription>Understand physical laws and phenomena</CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full" onClick={() => router.push('/subjects/physics')}>
                Explore Physics
              </Button>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Biology</CardTitle>
              <CardDescription>Study living organisms and systems</CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full" onClick={() => router.push('/subjects/biology')}>
                Explore Biology
              </Button>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Mathematics</CardTitle>
              <CardDescription>Solve complex mathematical problems</CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full" onClick={() => router.push('/subjects/mathematics')}>
                Explore Mathematics
              </Button>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Economics</CardTitle>
              <CardDescription>Learn economic principles and theories</CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full" onClick={() => router.push('/subjects/economics')}>
                Explore Economics
              </Button>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>English</CardTitle>
              <CardDescription>Improve language and literature skills</CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full" onClick={() => router.push('/subjects/english')}>
                Explore English
              </Button>
            </CardContent>
          </Card>
        </section>
        <section className="container py-8">
          <div className="mx-auto flex max-w-[980px] flex-col items-center gap-2 py-8 md:py-12 md:pb-8 lg:py-24 lg:pb-20">
            <h2 className="text-center text-3xl font-bold leading-tight tracking-tighter md:text-5xl lg:leading-[1.1]">
              Ask Your Question
            </h2>
            <p className="max-w-[750px] text-center text-lg text-muted-foreground sm:text-xl">
              Get instant help with any IGCSE subject. Our AI assistant is here to help you understand concepts better.
            </p>
          </div>
          <div className="mx-auto w-full max-w-[750px]">
            <form onSubmit={handleSendMessage} className="grid w-full items-start gap-4">
              <div className="grid gap-2">
                <Label htmlFor="message">Your Question</Label>
                <Textarea
                  id="message"
                  placeholder="Type your question here..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
              <Button type="submit">Send Message</Button>
            </form>
          </div>
        </section>
      </main>
      <footer className="border-t py-6 md:py-0">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
          <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
            <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
              Built by{" "}
              <a
                href="https://github.com/yourusername"
                target="_blank"
                rel="noreferrer"
                className="font-medium underline underline-offset-4"
              >
                Your Name
              </a>
              . The source code is available on{" "}
              <a
                href="https://github.com/yourusername/medha-ai"
                target="_blank"
                rel="noreferrer"
                className="font-medium underline underline-offset-4"
              >
                GitHub
              </a>
              .
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
