'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase/client'

export default function SupabaseTest() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')

  useEffect(() => {
    async function testConnection() {
      try {
        const { data, error } = await supabase.from('test').select('*').limit(1)
        if (error) throw error
        setStatus('success')
        setMessage('Supabase connection successful!')
      } catch (error) {
        setStatus('error')
        setMessage('Supabase connection failed. Check your environment variables.')
      }
    }

    testConnection()
  }, [])

  return (
    <div className="p-4 rounded-lg border">
      <h2 className="text-lg font-semibold mb-2">Supabase Connection Test</h2>
      <div className={`text-sm ${status === 'success' ? 'text-green-600' : status === 'error' ? 'text-red-600' : 'text-blue-600'}`}>
        {status === 'loading' ? 'Testing connection...' : message}
      </div>
    </div>
  )
} 