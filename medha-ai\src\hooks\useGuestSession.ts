import { useState, useEffect } from 'react'
import Cookies from 'js-cookie'

interface GuestSession {
  isGuest: boolean
  questionCount: number
  maxQuestions: number
  createdAt: string
}

export function useGuestSession() {
  const [guestSession, setGuestSession] = useState<GuestSession | null>(null)

  useEffect(() => {
    // Check for existing guest session in cookies
    const storedSession = Cookies.get('guestSession')
    if (storedSession) {
      try {
        setGuestSession(JSON.parse(storedSession))
      } catch (error) {
        console.error('Error parsing guest session:', error)
        Cookies.remove('guestSession')
      }
    }
  }, [])

  const incrementQuestionCount = () => {
    if (guestSession) {
      const updatedSession = {
        ...guestSession,
        questionCount: guestSession.questionCount + 1
      }
      Cookies.set('guestSession', JSON.stringify(updatedSession), { expires: 1 })
      setGuestSession(updatedSession)
    }
  }

  const canAskQuestion = () => {
    if (!guestSession) return false
    return guestSession.questionCount < guestSession.maxQuestions
  }

  const remainingQuestions = () => {
    if (!guestSession) return 0
    return guestSession.maxQuestions - guestSession.questionCount
  }

  const clearGuestSession = () => {
    Cookies.remove('guestSession')
    setGuestSession(null)
  }

  return {
    guestSession,
    incrementQuestionCount,
    canAskQuestion,
    remainingQuestions,
    clearGuestSession
  }
} 