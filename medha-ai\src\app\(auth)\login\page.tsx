'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { supabase } from '@/lib/supabase/client'
import Cookies from 'js-cookie'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setLoading(true)

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      // Clear any existing guest session
      Cookies.remove('guestSession')
      
      router.push('/')
      router.refresh()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleGuestMode = () => {
    // Clear any existing auth session
    Cookies.remove('sb-ysgxyussrjzliamxbvvy-auth-token.0')
    
    const guestSession = {
      isGuest: true,
      questionCount: 0,
      maxQuestions: 3,
      createdAt: new Date().toISOString()
    }
    // Set session cookie with sessionStorage to clear on browser close
    Cookies.set('guestSession', JSON.stringify(guestSession), { 
      expires: 1,
      sameSite: 'strict',
      secure: process.env.NODE_ENV === 'production'
    })
    router.push('/')
  }

  return (
    <div className="min-h-screen flex">
      {/* Left side - Hero section */}
      <div className="hidden lg:flex lg:w-[55%] relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-blue-800">
          <div className="absolute inset-0 bg-[url('/images/pattern.svg')] opacity-20"></div>
        </div>
        <div className="relative z-10 w-full h-full flex flex-col p-12">
          <div className="flex-1">
            <div className="flex items-center mb-12">
              <div className="w-12 h-12 rounded-xl bg-white/10 flex items-center justify-center backdrop-blur-sm">
                <span className="text-2xl">🎓</span>
              </div>
              <h1 className="ml-4 text-3xl font-bold text-white">Medha-AI</h1>
            </div>
            
            <div className="space-y-8 max-w-lg">
              <h2 className="text-4xl font-bold text-white leading-tight">
                Your Personal IGCSE Study Assistant
              </h2>
              <p className="text-xl text-blue-100">
                Empowering students with AI-powered learning tools for academic excellence
              </p>
              
              <div className="space-y-6 mt-12">
                <div className="flex items-center space-x-4 bg-white/10 p-4 rounded-xl backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                    <span className="text-xl">🎯</span>
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Personalized Learning</h3>
                    <p className="text-blue-100 text-sm">Tailored to your learning style</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4 bg-white/10 p-4 rounded-xl backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                    <span className="text-xl">📚</span>
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Comprehensive Coverage</h3>
                    <p className="text-blue-100 text-sm">All IGCSE subjects included</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4 bg-white/10 p-4 rounded-xl backdrop-blur-sm">
                  <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                    <span className="text-xl">💡</span>
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Smart Tools</h3>
                    <p className="text-blue-100 text-sm">AI-powered study assistance</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="text-white/60 text-sm">
            © 2024 Medha-AI. All rights reserved.
          </div>
        </div>
      </div>

      {/* Right side - Login form */}
      <div className="flex-1 flex items-center justify-center p-8 bg-gray-50 dark:bg-gray-900">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <div className="lg:hidden flex items-center justify-center mb-8">
              <div className="w-12 h-12 rounded-xl bg-blue-600 flex items-center justify-center">
                <span className="text-2xl text-white">🎓</span>
              </div>
              <h1 className="ml-4 text-3xl font-bold">Medha-AI</h1>
            </div>
            
            <div className="mb-8">
              <h2 className="text-3xl font-bold tracking-tight">Welcome Back</h2>
              <p className="mt-2 text-lg text-gray-600 dark:text-gray-400">
                Sign in to continue your learning journey
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg space-y-6">
            <form onSubmit={handleLogin} className="space-y-6">
              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Email address
                </label>
                <Input
                  id="email"
                  type="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="h-12 text-base"
                  placeholder="Enter your email"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Password
                </label>
                <Input
                  id="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="h-12 text-base"
                  placeholder="Enter your password"
                />
              </div>

              {error && (
                <div className="p-4 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-sm">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                disabled={loading}
                className="w-full h-12 text-base font-semibold"
              >
                {loading ? 'Signing in...' : 'Sign in'}
              </Button>
            </form>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200 dark:border-gray-700"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white dark:bg-gray-800 text-gray-500">Or</span>
              </div>
            </div>

            <Button
              onClick={handleGuestMode}
              variant="outline"
              className="w-full h-12 text-base font-semibold"
            >
              Continue as Guest
            </Button>

            <div className="text-center text-sm">
              <span className="text-gray-600 dark:text-gray-400">Don't have an account? </span>
              <Link 
                href="/register" 
                className="font-semibold text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Sign up
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 