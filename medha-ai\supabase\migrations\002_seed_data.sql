-- Insert initial subjects
INSERT INTO public.subjects (name, icon, description) VALUES
('Chemistry', '🧪', 'Study of matter, its properties, and transformations'),
('Physics', '⚡', 'Understanding the laws of nature and energy'),
('Biology', '🧬', 'Study of living organisms and their processes'),
('Mathematics', '📐', 'Numbers, algebra, geometry, and statistics'),
('Economics', '💰', 'Study of production, consumption, and wealth'),
('English', '📚', 'Language and literature studies');

-- Insert chapters for Chemistry
INSERT INTO public.chapters (subject_id, name, description, order_index)
SELECT id, 'Atomic Structure', 'Understanding atoms and their structure', 1
FROM public.subjects WHERE name = 'Chemistry';

INSERT INTO public.chapters (subject_id, name, description, order_index)
SELECT id, 'Bonding', 'Chemical bonds and their types', 2
FROM public.subjects WHERE name = 'Chemistry';

INSERT INTO public.chapters (subject_id, name, description, order_index)
SELECT id, 'Chemical Reactions', 'Types of reactions and their mechanisms', 3
FROM public.subjects WHERE name = 'Chemistry';

-- Insert chapters for Physics
INSERT INTO public.chapters (subject_id, name, description, order_index)
SELECT id, 'Forces and Motion', 'Understanding forces and their effects', 1
FROM public.subjects WHERE name = 'Physics';

INSERT INTO public.chapters (subject_id, name, description, order_index)
SELECT id, 'Energy', 'Forms of energy and energy conservation', 2
FROM public.subjects WHERE name = 'Physics';

INSERT INTO public.chapters (subject_id, name, description, order_index)
SELECT id, 'Waves', 'Properties and behavior of waves', 3
FROM public.subjects WHERE name = 'Physics';

-- Insert sample flashcards for Chemistry - Atomic Structure
INSERT INTO public.flashcards (chapter_id, question, answer, examples, difficulty)
SELECT 
    c.id,
    'What is an atom?',
    'The smallest unit of matter that retains the properties of an element',
    ARRAY['Atoms are made up of protons, neutrons, and electrons', 'Atoms are electrically neutral'],
    'easy'
FROM public.chapters c
JOIN public.subjects s ON c.subject_id = s.id
WHERE s.name = 'Chemistry' AND c.name = 'Atomic Structure';

-- Insert sample quiz for Chemistry - Atomic Structure
INSERT INTO public.quizzes (chapter_id, title, description, difficulty)
SELECT 
    c.id,
    'Atomic Structure Basics',
    'Test your knowledge of atomic structure fundamentals',
    'easy'
FROM public.chapters c
JOIN public.subjects s ON c.subject_id = s.id
WHERE s.name = 'Chemistry' AND c.name = 'Atomic Structure';

-- Insert sample quiz questions
INSERT INTO public.quiz_questions (quiz_id, question, options, correct_answer, explanation)
SELECT 
    q.id,
    'What is the charge of a proton?',
    ARRAY['Positive', 'Negative', 'Neutral', 'Variable'],
    0,
    'Protons have a positive charge of +1'
FROM public.quizzes q
JOIN public.chapters c ON q.chapter_id = c.id
WHERE c.name = 'Atomic Structure'; 