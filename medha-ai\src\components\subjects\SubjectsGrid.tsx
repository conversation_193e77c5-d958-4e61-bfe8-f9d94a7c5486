'use client'

import SubjectTile from './SubjectTile'

const SUBJECTS = [
  {
    id: 'chemistry',
    name: 'Chemistry',
    icon: '🧪',
    description: 'Study of matter, its properties, and transformations'
  },
  {
    id: 'physics',
    name: 'Physics',
    icon: '⚡',
    description: 'Understanding the laws of nature and energy'
  },
  {
    id: 'biology',
    name: 'Biology',
    icon: '🧬',
    description: 'Study of living organisms and their processes'
  },
  {
    id: 'mathematics',
    name: 'Mathematics',
    icon: '📐',
    description: 'Numbers, algebra, geometry, and statistics'
  },
  {
    id: 'economics',
    name: 'Economics',
    icon: '💰',
    description: 'Study of production, consumption, and wealth'
  },
  {
    id: 'english',
    name: 'English',
    icon: '📚',
    description: 'Language and literature studies'
  }
]

export default function SubjectsGrid() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {SUBJECTS.map((subject) => (
        <SubjectTile
          key={subject.id}
          {...subject}
        />
      ))}
    </div>
  )
} 